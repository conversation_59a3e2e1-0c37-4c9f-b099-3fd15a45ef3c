# Rust Shenanigans 🦀

Welcome to my journey of learning Rust! This repository documents my adventures, experiments, and progress as I dive into the world of systems programming with Rust.

## About This Project

This repository serves as a collection of exercises, tutorials, and personal experiments as I learn Rust from the ground up. It's a mix of structured learning materials and my own explorations into what makes Rust such a powerful and safe systems programming language.

## Learning Path

### 📚 Completed Exercises

#### 1. FH-WS (Workshop)
- **Status:** ✅ Completed
- **Description:** A comprehensive 10-hour workshop that went in-depth into learning Rust with minimal actual exercises, focusing more on understanding core concepts and language fundamentals.

#### 2. FHLUG Session
- **Status:** ✅ Completed
- **Description:** One big session conducted in a larger group setting, designed to showcase what Rust can do and how it works. During this session, we created 3 experimental projects to explore different aspects of Rust.

### 📚 Current Exercises

#### 3. Rustlings
- **Status:** 🔄 In Progress
- **Repository:** [rust-lang/rustlings](https://github.com/rust-lang/rustlings)
- **Description:** Small exercises designed to get you used to reading and writing Rust code. These bite-sized exercises cover various Rust concepts from basic syntax to advanced features.

*The rustlings exercises help solidify understanding through hands-on practice.*

### 🎯 Planned Learning Sources

*Learning sources and materials to be added later.*

## Project Structure

```
rust-shenanigans/
├── fh-ws/           # FH-WS workshop materials (to be added)
├── fhlug/           # FHLUG session experimental projects
│   ├── fhlug1/      # First experimental project
│   ├── fhlug2/      # Second experimental project
│   └── fhlug3/      # Third experimental project
├── rustlings/       # Rustlings exercises
└── README.md        # This file
```

## Progress Tracking

- **FH-WS Workshop:** ✅ Completed
- **FHLUG Session:** ✅ Completed
- **Rustlings:** 🔄 In Progress

## Resources

*Resources to be added later.*

## Notes

This is a personal learning repository, so expect:
- Experimental code that might not always work
- Comments and notes to myself
- Gradual improvement over time

Feel free to explore, but remember this is a learning journey - not production code! 😄

---

*"Rust is a language that empowers everyone to build reliable and efficient software."* - The Rust Team
