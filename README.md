# Rust Shenanigans 🦀

Welcome to my journey of learning Rust! This repository documents my adventures, experiments, and progress as I dive into the world of systems programming with Rust.

## About This Project

This repository serves as a collection of exercises, tutorials, and personal experiments as I learn Rust from the ground up. It's a mix of structured learning materials and my own explorations into what makes Rust such a powerful and safe systems programming language.

## Learning Path

### 📚 Current Exercises

#### 1. FHLUG Sessions
**Status:** In Progress  
**Description:** General Rust overview sessions conducted in a larger group setting. These are live tutorial sessions designed to showcase what Rust can do and how it works.

- **fhlug1/**: First session materials
- **fhlug2/**: Second session materials  
- **fhlug3/**: Third session materials

*These sessions provide a broad overview of Rust concepts and practical demonstrations.*

#### 2. Rustlings
**Status:** Planned  
**Repository:** [rust-lang/rustlings](https://github.com/rust-lang/rustlings)  
**Description:** Small exercises designed to get you used to reading and writing Rust code. These bite-sized exercises cover various Rust concepts from basic syntax to advanced features.

*The rustlings exercises will help solidify my understanding through hands-on practice.*

### 🎯 Planned Learning Topics

- [ ] Basic Rust syntax and concepts
- [ ] Ownership and borrowing
- [ ] Pattern matching and enums
- [ ] Error handling
- [ ] Structs and traits
- [ ] Generics and lifetimes
- [ ] Concurrency and async programming
- [ ] Memory management
- [ ] Testing in Rust
- [ ] Building real-world projects

## Project Structure

```
rust-shenanigans/
├── fhlug/           # FHLUG tutorial sessions
│   ├── fhlug1/      # Session 1 materials
│   ├── fhlug2/      # Session 2 materials
│   └── fhlug3/      # Session 3 materials
├── rustlings/       # Rustlings exercises
└── README.md        # This file
```

## Getting Started

### Prerequisites
- Rust installed (via [rustup](https://rustup.rs/))
- A curious mind and patience for learning! 🧠

### Running the Code
Each directory contains its own exercises and examples. Navigate to the specific folder and follow the instructions within.

## Progress Tracking

- **FHLUG Sessions:** 🔄 In Progress
- **Rustlings:** 📋 Not Started

## Resources

- [The Rust Programming Language Book](https://doc.rust-lang.org/book/)
- [Rust by Example](https://doc.rust-lang.org/rust-by-example/)
- [Rustlings Repository](https://github.com/rust-lang/rustlings)
- [Rust Documentation](https://doc.rust-lang.org/)

## Notes

This is a personal learning repository, so expect:
- Experimental code that might not always work
- Comments and notes to myself
- Gradual improvement over time
- Lots of "aha!" moments documented

Feel free to explore, but remember this is a learning journey - not production code! 😄

---

*"Rust is a language that empowers everyone to build reliable and efficient software."* - The Rust Team
